"""Comprehensive error handling and validation system for SRE agents."""

from typing import Dict, Any, List, Optional, Union, Callable
from datetime import datetime
import traceback
import logging
from enum import Enum
from functools import wraps

class ErrorSeverity(str, Enum):
    """Error severity levels for incident management."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"

class ErrorCategory(str, Enum):
    """Categories of errors in SRE operations."""
    VALIDATION = "validation"
    COMMUNICATION = "communication"
    TOOL_EXECUTION = "tool_execution"
    DATA_INTEGRITY = "data_integrity"
    TIMEOUT = "timeout"
    PERMISSION = "permission"
    RESOURCE = "resource"
    CONFIGURATION = "configuration"

class SREError(Exception):
    """Custom exception class for SRE operations."""
    
    def __init__(self, message: str, category: ErrorCategory, 
                 severity: ErrorSeverity, context: Optional[Dict[str, Any]] = None,
                 recoverable: bool = True):
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.context = context or {}
        self.recoverable = recoverable
        self.timestamp = datetime.now().isoformat()
        self.error_id = f"SRE-{int(datetime.now().timestamp())}"

class ErrorHandler:
    """Centralized error handling for SRE agents."""
    
    def __init__(self):
        self.error_log = []
        self.recovery_strategies = {}
        self.escalation_thresholds = {
            ErrorSeverity.CRITICAL: 0,  # Immediate escalation
            ErrorSeverity.HIGH: 2,      # Escalate after 2 occurrences
            ErrorSeverity.MEDIUM: 5,    # Escalate after 5 occurrences
            ErrorSeverity.LOW: 10       # Escalate after 10 occurrences
        }
        
        # Set up logging
        self.logger = logging.getLogger("sre_error_handler")
        self.logger.setLevel(logging.INFO)
    
    def handle_error(self, error: Union[Exception, SREError], 
                    agent_name: str, operation: str,
                    context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Handle errors with appropriate logging, recovery, and escalation."""
        
        # Convert regular exceptions to SREError
        if not isinstance(error, SREError):
            sre_error = SREError(
                message=str(error),
                category=ErrorCategory.TOOL_EXECUTION,
                severity=ErrorSeverity.MEDIUM,
                context=context
            )
        else:
            sre_error = error
        
        # Log the error
        error_entry = {
            "error_id": sre_error.error_id,
            "timestamp": sre_error.timestamp,
            "agent_name": agent_name,
            "operation": operation,
            "message": sre_error.message,
            "category": sre_error.category.value,
            "severity": sre_error.severity.value,
            "context": sre_error.context,
            "recoverable": sre_error.recoverable,
            "stack_trace": traceback.format_exc() if not isinstance(error, SREError) else None
        }
        
        self.error_log.append(error_entry)
        self.logger.error(f"SRE Error [{sre_error.error_id}]: {sre_error.message}")
        
        # Attempt recovery if possible
        recovery_result = self._attempt_recovery(sre_error, agent_name, operation)
        
        # Check escalation requirements
        escalation_needed = self._check_escalation(sre_error, agent_name)
        
        return {
            "error_handled": True,
            "error_id": sre_error.error_id,
            "severity": sre_error.severity.value,
            "recoverable": sre_error.recoverable,
            "recovery_attempted": recovery_result["attempted"],
            "recovery_successful": recovery_result["successful"],
            "escalation_needed": escalation_needed,
            "user_message": self._generate_user_message(sre_error, recovery_result, escalation_needed)
        }
    
    def _attempt_recovery(self, error: SREError, agent_name: str, operation: str) -> Dict[str, Any]:
        """Attempt to recover from the error using predefined strategies."""
        recovery_key = f"{error.category.value}_{operation}"
        
        if recovery_key in self.recovery_strategies:
            try:
                recovery_function = self.recovery_strategies[recovery_key]
                recovery_result = recovery_function(error, agent_name, operation)
                return {"attempted": True, "successful": True, "result": recovery_result}
            except Exception as recovery_error:
                self.logger.error(f"Recovery failed for {recovery_key}: {recovery_error}")
                return {"attempted": True, "successful": False, "error": str(recovery_error)}
        
        return {"attempted": False, "successful": False}
    
    def _check_escalation(self, error: SREError, agent_name: str) -> bool:
        """Check if error requires escalation based on severity and frequency."""
        # Count recent errors of same severity from same agent
        recent_errors = [
            e for e in self.error_log[-50:]  # Check last 50 errors
            if e["agent_name"] == agent_name and e["severity"] == error.severity.value
        ]
        
        threshold = self.escalation_thresholds.get(error.severity, 5)
        return len(recent_errors) >= threshold
    
    def _generate_user_message(self, error: SREError, recovery_result: Dict[str, Any], 
                              escalation_needed: bool) -> str:
        """Generate user-friendly error message."""
        base_message = f"⚠️ **{error.severity.value.upper()} ERROR**: {error.message}"
        
        if recovery_result["attempted"]:
            if recovery_result["successful"]:
                base_message += "\n✅ **Recovery**: Automatic recovery was successful."
            else:
                base_message += "\n❌ **Recovery**: Automatic recovery failed."
        
        if escalation_needed:
            base_message += "\n🚨 **Escalation**: This error requires immediate attention from the SRE team."
        
        if error.recoverable:
            base_message += "\n💡 **Next Steps**: You may retry the operation or try an alternative approach."
        else:
            base_message += "\n⛔ **Action Required**: Manual intervention is required to resolve this issue."
        
        return base_message
    
    def register_recovery_strategy(self, category: ErrorCategory, operation: str, 
                                 recovery_function: Callable) -> None:
        """Register a recovery strategy for specific error types."""
        recovery_key = f"{category.value}_{operation}"
        self.recovery_strategies[recovery_key] = recovery_function
    
    def get_error_statistics(self, agent_name: Optional[str] = None) -> Dict[str, Any]:
        """Get error statistics for monitoring and analysis."""
        relevant_errors = self.error_log
        if agent_name:
            relevant_errors = [e for e in self.error_log if e["agent_name"] == agent_name]
        
        if not relevant_errors:
            return {"total_errors": 0}
        
        # Calculate statistics
        severity_counts = {}
        category_counts = {}
        
        for error in relevant_errors:
            severity = error["severity"]
            category = error["category"]
            
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            category_counts[category] = category_counts.get(category, 0) + 1
        
        return {
            "total_errors": len(relevant_errors),
            "severity_breakdown": severity_counts,
            "category_breakdown": category_counts,
            "recent_errors": relevant_errors[-10:],  # Last 10 errors
            "error_rate": len(relevant_errors) / max(1, len(self.error_log)) * 100
        }

# Global error handler instance
error_handler = ErrorHandler()

def sre_error_handler(operation: str):
    """Decorator for automatic error handling in SRE operations."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Extract agent name from function context if available
                agent_name = getattr(func, '__self__', {}).get('name', 'unknown_agent')
                if hasattr(args[0], 'name'):
                    agent_name = args[0].name
                
                error_result = error_handler.handle_error(e, agent_name, operation)
                
                # Re-raise if not recoverable or recovery failed
                if not error_result["recovery_successful"] and isinstance(e, SREError) and not e.recoverable:
                    raise e
                
                return error_result
        return wrapper
    return decorator

class ValidationError(SREError):
    """Specific error type for validation failures."""
    
    def __init__(self, message: str, field: str, expected: Any, actual: Any):
        super().__init__(
            message=message,
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.MEDIUM,
            context={"field": field, "expected": expected, "actual": actual}
        )

class DataValidator:
    """Validation utilities for SRE data."""
    
    @staticmethod
    def validate_incident_data(data: Dict[str, Any]) -> List[ValidationError]:
        """Validate incident data structure and content."""
        errors = []
        
        # Required fields validation
        required_fields = ["title", "description", "severity"]
        for field in required_fields:
            if field not in data or not data[field]:
                errors.append(ValidationError(
                    f"Missing required field: {field}",
                    field, "non-empty value", data.get(field, "missing")
                ))
        
        # Severity validation
        if "severity" in data:
            valid_severities = ["P0", "P1", "P2", "P3"]
            if data["severity"] not in valid_severities:
                errors.append(ValidationError(
                    f"Invalid severity value",
                    "severity", valid_severities, data["severity"]
                ))
        
        # Service validation
        if "affected_services" in data:
            if not isinstance(data["affected_services"], list):
                errors.append(ValidationError(
                    "affected_services must be a list",
                    "affected_services", "list", type(data["affected_services"]).__name__
                ))
        
        return errors
    
    @staticmethod
    def validate_time_range(start_time: str, end_time: str) -> List[ValidationError]:
        """Validate time range parameters."""
        errors = []
        
        try:
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            
            if start_dt >= end_dt:
                errors.append(ValidationError(
                    "Start time must be before end time",
                    "time_range", "start < end", f"start: {start_time}, end: {end_time}"
                ))
                
        except ValueError as e:
            errors.append(ValidationError(
                f"Invalid time format: {e}",
                "time_format", "ISO format", f"start: {start_time}, end: {end_time}"
            ))
        
        return errors

def get_error_handler() -> ErrorHandler:
    """Get the global error handler instance."""
    return error_handler
