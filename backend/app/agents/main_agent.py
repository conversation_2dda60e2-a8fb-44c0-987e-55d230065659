from google.adk.agents import Agent
from google.adk.models.lite_llm import Lite<PERSON>lm
from google.adk.tools.agent_tool import AgentTool

from agents.guardrail import block_keyword_guardrail
from agents.sub_agents import (
    # Core workflow orchestration
    workflow_orchestrator_agent,

    # Original agents (enhanced)
    incident_manager_agent,
    log_analytics_agent,
    preference_agent,
    report_agent,
    root_cause_analyzer,
    runbook_generator_agent,
    time_agent,

    # New specialized SRE agents
    incident_triage_agent,
    system_health_analyzer_agent,
    metrics_analyzer_agent,
    validation_agent,
    notification_agent,
)

from . import prompt

# AGENT_MODEL = "ollama/qwen3:4b"
AGENT_MODEL = "gemini/gemini-2.0-flash"
# AGENT_MODEL = "gemini/gemini-2.5-flash-preview-05-20"

coordinator_agent = Agent(
    name="sre_incident_coordinator",
    model=LiteLlm(AGENT_MODEL),
    description="Advanced SRE Incident Management Coordinator with workflow orchestration capabilities. Routes complex incident scenarios to appropriate workflow patterns and specialized agents for comprehensive incident response.",
    instruction=prompt.INSTRUCTION,
    sub_agents=[
        time_agent,
        preference_agent,
        notification_agent,
        validation_agent,
    ],
    tools=[
        # Primary workflow orchestrator
        AgentTool(agent=workflow_orchestrator_agent, skip_summarization=False),

        # Specialized analysis agents
        AgentTool(agent=incident_triage_agent, skip_summarization=False),
        AgentTool(agent=system_health_analyzer_agent, skip_summarization=False),
        AgentTool(agent=metrics_analyzer_agent, skip_summarization=False),

        # Enhanced original agents
        AgentTool(agent=runbook_generator_agent, skip_summarization=False),
        AgentTool(agent=root_cause_analyzer, skip_summarization=False),
        AgentTool(agent=log_analytics_agent, skip_summarization=False),
        AgentTool(agent=incident_manager_agent, skip_summarization=False),
        AgentTool(agent=report_agent, skip_summarization=False),
    ],
    before_model_callback=block_keyword_guardrail,
)
