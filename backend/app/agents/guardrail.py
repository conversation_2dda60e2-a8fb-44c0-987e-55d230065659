from typing import Optional, List, Dict, Any
import re
import json
from datetime import datetime

from google.adk.agents.callback_context import CallbackContext
from google.adk.models.llm_request import LlmRequest
from google.adk.models.llm_response import LlmResponse
from google.genai import types  # For creating response content


class SREGuardrailSystem:
    """Enhanced guardrail system for SRE incident management with safety checks and validation."""

    def __init__(self):
        # Dangerous keywords that should be blocked
        self.blocked_keywords = [
            "BLOCK", "rm -rf", "DROP DATABASE", "DELETE FROM", "TRUNCATE",
            "shutdown", "reboot", "kill -9", "format", "fdisk"
        ]

        # High-risk operations that require confirmation
        self.high_risk_patterns = [
            r"delete.*production",
            r"drop.*table",
            r"restart.*database",
            r"modify.*firewall",
            r"change.*dns"
        ]

        # Incident severity validation
        self.valid_severities = ["P0", "P1", "P2", "P3"]

        # Rate limiting for API calls
        self.rate_limits = {
            "max_requests_per_minute": 60,
            "max_requests_per_hour": 1000
        }

    def validate_incident_data(self, text: str) -> Dict[str, Any]:
        """Validate incident data for completeness and accuracy."""
        validation_result = {
            "is_valid": True,
            "warnings": [],
            "errors": [],
            "suggestions": []
        }

        # Check for required incident fields
        required_fields = ["title", "description", "severity", "affected_services"]
        missing_fields = []

        for field in required_fields:
            if field.lower() not in text.lower():
                missing_fields.append(field)

        if missing_fields:
            validation_result["warnings"].append(f"Missing recommended fields: {', '.join(missing_fields)}")
            validation_result["suggestions"].append("Consider providing complete incident information for better analysis")

        # Validate severity if present
        severity_match = re.search(r"severity[:\s]*(P[0-3])", text, re.IGNORECASE)
        if severity_match:
            severity = severity_match.group(1).upper()
            if severity not in self.valid_severities:
                validation_result["errors"].append(f"Invalid severity '{severity}'. Must be one of: {', '.join(self.valid_severities)}")
                validation_result["is_valid"] = False

        return validation_result

    def check_high_risk_operations(self, text: str) -> Dict[str, Any]:
        """Check for high-risk operations that require special handling."""
        risk_assessment = {
            "risk_level": "low",
            "detected_risks": [],
            "requires_confirmation": False,
            "safety_recommendations": []
        }

        # Check for high-risk patterns
        for pattern in self.high_risk_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                risk_assessment["detected_risks"].append(pattern)
                risk_assessment["risk_level"] = "high"
                risk_assessment["requires_confirmation"] = True

        # Check for dangerous keywords
        for keyword in self.blocked_keywords:
            if keyword.lower() in text.lower():
                risk_assessment["detected_risks"].append(f"Dangerous keyword: {keyword}")
                risk_assessment["risk_level"] = "critical"
                risk_assessment["requires_confirmation"] = True

        # Add safety recommendations for high-risk operations
        if risk_assessment["risk_level"] in ["high", "critical"]:
            risk_assessment["safety_recommendations"] = [
                "Ensure you have proper backups before proceeding",
                "Verify the operation in a test environment first",
                "Have a rollback plan ready",
                "Consider getting approval from a senior team member"
            ]

        return risk_assessment


def enhanced_sre_guardrail(
    callback_context: CallbackContext, llm_request: LlmRequest
) -> Optional[LlmResponse]:
    """
    Enhanced guardrail system for SRE operations with comprehensive safety checks.
    """
    agent_name = callback_context.agent_name
    guardrail_system = SREGuardrailSystem()

    print(f"--- Enhanced SRE Guardrail: Running for agent: {agent_name} ---")

    # Extract the text from the latest user message
    last_user_message_text = ""
    if llm_request.contents:
        for content in reversed(llm_request.contents):
            if content.role == "user" and content.parts:
                if content.parts[0].text:
                    last_user_message_text = content.parts[0].text
                    break

    if not last_user_message_text:
        return None

    print(f"--- Guardrail: Analyzing message: '{last_user_message_text[:100]}...' ---")

    # Validate incident data
    incident_validation = guardrail_system.validate_incident_data(last_user_message_text)

    # Check for high-risk operations
    risk_assessment = guardrail_system.check_high_risk_operations(last_user_message_text)

    # Store validation results in state for other agents to use
    callback_context.state["guardrail_validation"] = {
        "incident_validation": incident_validation,
        "risk_assessment": risk_assessment,
        "timestamp": datetime.now().isoformat()
    }

    # Block critical risk operations
    if risk_assessment["risk_level"] == "critical":
        print(f"--- Guardrail: BLOCKING critical risk operation ---")
        callback_context.state["guardrail_blocked"] = True

        return LlmResponse(
            content=types.Content(
                role="model",
                parts=[
                    types.Part(
                        text=f"""🚨 **OPERATION BLOCKED FOR SAFETY**

I've detected potentially dangerous operations in your request that could cause system damage:

**Detected Risks:**
{chr(10).join(f"• {risk}" for risk in risk_assessment["detected_risks"])}

**Safety Recommendations:**
{chr(10).join(f"• {rec}" for rec in risk_assessment["safety_recommendations"])}

Please review your request and ensure you have proper safeguards in place. If this operation is intentional and safe, please rephrase your request with explicit safety confirmations."""
                    )
                ],
            )
        )

    # Warn about high-risk operations but allow to proceed
    if risk_assessment["risk_level"] == "high":
        print(f"--- Guardrail: WARNING about high-risk operation ---")
        callback_context.state["guardrail_warning_issued"] = True

        # Add warning to the request context but allow processing to continue
        warning_message = f"""⚠️ **HIGH-RISK OPERATION DETECTED**

The following risks were identified:
{chr(10).join(f"• {risk}" for risk in risk_assessment["detected_risks"])}

Please ensure proper safety measures are in place."""

        # Store warning for the agent to include in its response
        callback_context.state["guardrail_warning_message"] = warning_message

    # Log validation warnings
    if incident_validation["warnings"]:
        print(f"--- Guardrail: Incident validation warnings: {incident_validation['warnings']} ---")
        callback_context.state["guardrail_incident_warnings"] = incident_validation["warnings"]

    # Return None to allow processing to continue
    return None


# Keep the original function for backward compatibility
def block_keyword_guardrail(
    callback_context: CallbackContext, llm_request: LlmRequest
) -> Optional[LlmResponse]:
    """Original guardrail function - now delegates to enhanced version."""
    return enhanced_sre_guardrail(callback_context, llm_request)
