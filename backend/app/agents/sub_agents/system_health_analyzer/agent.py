from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm
from pydantic import BaseModel, Field
from typing import List, Dict, Optional

from . import prompt, tools

class ServiceHealth(BaseModel):
    service_name: str = Field(..., description="Name of the service")
    status: str = Field(..., description="Current status (healthy/degraded/critical/unknown)")
    response_time: Optional[float] = Field(None, description="Average response time in ms")
    error_rate: Optional[float] = Field(None, description="Error rate percentage")
    availability: Optional[float] = Field(None, description="Availability percentage")
    last_check: str = Field(..., description="Timestamp of last health check")

class SystemHealthReport(BaseModel):
    overall_status: str = Field(..., description="Overall system health (healthy/degraded/critical)")
    healthy_services: List[ServiceHealth] = Field(..., description="List of healthy services")
    degraded_services: List[ServiceHealth] = Field(..., description="List of degraded services")
    critical_services: List[ServiceHealth] = Field(..., description="List of critical services")
    infrastructure_status: Dict[str, str] = Field(..., description="Infrastructure component statuses")
    dependency_health: Dict[str, str] = Field(..., description="External dependency health")
    active_alerts: List[str] = Field(..., description="Currently active system alerts")
    health_trends: Dict[str, str] = Field(..., description="Health trends over time")
    recommendations: List[str] = Field(..., description="Health improvement recommendations")

AGENT_MODEL = "gemini/gemini-2.0-flash"

system_health_analyzer_agent = LlmAgent(
    name="system_health_analyzer",
    model=LiteLlm(AGENT_MODEL),
    description="Specialized agent for comprehensive system health analysis, monitoring service status, and infrastructure health assessment.",
    instruction=prompt.INSTRUCTION,
    output_schema=SystemHealthReport,
    output_key="system_health_results",
    tools=[
        tools.check_service_health,
        tools.monitor_infrastructure_status,
        tools.analyze_dependency_health,
        tools.collect_system_alerts,
        tools.assess_health_trends,
    ],
)
