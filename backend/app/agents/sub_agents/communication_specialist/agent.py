from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm

AGENT_MODEL = "gemini/gemini-2.0-flash"

communication_specialist_agent = LlmAgent(
    name="communication_specialist",
    model=LiteLlm(AGENT_MODEL),
    description="Manages stakeholder communications, status updates, and incident notifications.",
    instruction="You are a communication specialist. Handle all stakeholder communications, provide clear status updates, and manage incident notifications appropriately.",
    tools=[],
)
