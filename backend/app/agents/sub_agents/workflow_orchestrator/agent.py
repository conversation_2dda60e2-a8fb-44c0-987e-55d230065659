from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm
from google.adk.tools.agent_tool import AgentTool

from . import prompt, tools

AGENT_MODEL = "gemini/gemini-2.0-flash"

# Import workflow agents (will be created)
from ..incident_response_pipeline.agent import incident_response_pipeline_agent
from ..multi_source_analysis.agent import multi_source_analysis_agent
from ..recovery_monitoring.agent import recovery_monitoring_agent

workflow_orchestrator_agent = Agent(
    name="workflow_orchestrator",
    model=LiteLlm(AGENT_MODEL),
    description="Orchestrates complex incident management workflows by coordinating Sequential, Parallel, and Loop agents for comprehensive SRE operations.",
    instruction=prompt.INSTRUCTION,
    tools=[
        AgentTool(agent=incident_response_pipeline_agent, skip_summarization=False),
        AgentTool(agent=multi_source_analysis_agent, skip_summarization=False),
        Agent<PERSON><PERSON>(agent=recovery_monitoring_agent, skip_summarization=False),
        tools.determine_workflow_type,
        tools.validate_workflow_inputs,
        tools.track_workflow_progress,
    ],
)
