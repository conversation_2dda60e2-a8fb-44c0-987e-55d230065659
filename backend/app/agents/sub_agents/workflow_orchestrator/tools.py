"""Tools for the workflow orchestrator agent."""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum
import json
from datetime import datetime

class IncidentSeverity(str, Enum):
    P0 = "P0"  # Critical - Complete service outage
    P1 = "P1"  # High - Major functionality impacted
    P2 = "P2"  # Medium - Minor functionality impacted
    P3 = "P3"  # Low - Minimal impact

class IncidentType(str, Enum):
    SERVICE_OUTAGE = "service_outage"
    PERFORMANCE_DEGRADATION = "performance_degradation"
    SECURITY_INCIDENT = "security_incident"
    DATA_CORRUPTION = "data_corruption"
    INFRASTRUCTURE_FAILURE = "infrastructure_failure"
    DEPLOYMENT_ISSUE = "deployment_issue"
    CONFIGURATION_ERROR = "configuration_error"

class WorkflowType(str, Enum):
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    LOOP = "loop"

class WorkflowRecommendation(BaseModel):
    workflow_type: WorkflowType = Field(..., description="Recommended workflow pattern")
    confidence: float = Field(..., description="Confidence score (0.0-1.0)")
    reasoning: str = Field(..., description="Explanation for the recommendation")
    estimated_duration: str = Field(..., description="Estimated completion time")
    required_agents: List[str] = Field(..., description="List of required specialized agents")

class ValidationResult(BaseModel):
    is_valid: bool = Field(..., description="Whether inputs are valid")
    missing_fields: List[str] = Field(default=[], description="List of missing required fields")
    validation_errors: List[str] = Field(default=[], description="List of validation errors")
    suggestions: List[str] = Field(default=[], description="Suggestions for improvement")

class WorkflowProgress(BaseModel):
    workflow_id: str = Field(..., description="Unique workflow identifier")
    status: str = Field(..., description="Current workflow status")
    progress_percentage: float = Field(..., description="Completion percentage (0.0-100.0)")
    current_step: str = Field(..., description="Current step being executed")
    elapsed_time: str = Field(..., description="Time elapsed since start")
    estimated_remaining: str = Field(..., description="Estimated time remaining")
    issues: List[str] = Field(default=[], description="Any issues or warnings")

def determine_workflow_type(
    incident_title: str,
    incident_description: str,
    incident_severity: str,
    affected_services: List[str],
    incident_type: Optional[str] = None,
    time_constraints: Optional[str] = None,
    available_data_sources: Optional[List[str]] = None
) -> WorkflowRecommendation:
    """
    Analyze incident characteristics to determine the most appropriate workflow pattern.
    
    Args:
        incident_title: Brief title of the incident
        incident_description: Detailed description of the incident
        incident_severity: Incident severity (P0, P1, P2, P3)
        affected_services: List of affected services/systems
        incident_type: Type of incident (optional)
        time_constraints: Any time constraints or SLA requirements
        available_data_sources: Available data sources for analysis
    
    Returns:
        WorkflowRecommendation with suggested workflow pattern and details
    """
    
    # Analyze incident characteristics
    severity = IncidentSeverity(incident_severity) if incident_severity in [e.value for e in IncidentSeverity] else IncidentSeverity.P2
    
    # Determine workflow based on characteristics
    if severity in [IncidentSeverity.P0, IncidentSeverity.P1]:
        # Critical incidents need structured sequential response
        if len(affected_services) > 3 or "multiple" in incident_description.lower():
            # Multiple services affected - need parallel analysis first
            return WorkflowRecommendation(
                workflow_type=WorkflowType.PARALLEL,
                confidence=0.85,
                reasoning="High severity incident affecting multiple services requires parallel analysis for rapid assessment",
                estimated_duration="15-30 minutes",
                required_agents=["multi_source_analysis", "incident_triage", "system_health_analyzer", "metrics_analyzer"]
            )
        else:
            # Single service critical incident - sequential response
            return WorkflowRecommendation(
                workflow_type=WorkflowType.SEQUENTIAL,
                confidence=0.90,
                reasoning="Critical incident requires structured sequential response with proper escalation",
                estimated_duration="30-60 minutes",
                required_agents=["incident_response_pipeline", "incident_triage", "root_cause_analyzer", "auto_remediation"]
            )
    
    elif "monitoring" in incident_description.lower() or "recovery" in incident_description.lower():
        # Recovery or monitoring scenarios need loop workflows
        return WorkflowRecommendation(
            workflow_type=WorkflowType.LOOP,
            confidence=0.80,
            reasoning="Recovery or monitoring scenario requires continuous evaluation and iterative actions",
            estimated_duration="Ongoing (30-120 minutes)",
            required_agents=["recovery_monitoring", "system_health_analyzer", "recovery_coordinator"]
        )
    
    elif len(affected_services) > 2 or (available_data_sources and len(available_data_sources) > 3):
        # Multiple systems or data sources - parallel analysis
        return WorkflowRecommendation(
            workflow_type=WorkflowType.PARALLEL,
            confidence=0.75,
            reasoning="Multiple systems or data sources require parallel analysis for efficiency",
            estimated_duration="20-45 minutes",
            required_agents=["multi_source_analysis", "log_analytics", "metrics_analyzer", "system_health_analyzer"]
        )
    
    else:
        # Default to sequential for standard incidents
        return WorkflowRecommendation(
            workflow_type=WorkflowType.SEQUENTIAL,
            confidence=0.70,
            reasoning="Standard incident response following established procedures",
            estimated_duration="45-90 minutes",
            required_agents=["incident_response_pipeline", "incident_manager", "root_cause_analyzer"]
        )

def validate_workflow_inputs(
    incident_data: Dict[str, Any],
    workflow_type: str,
    required_fields: Optional[List[str]] = None
) -> ValidationResult:
    """
    Validate that all required inputs are available and properly formatted for the workflow.
    
    Args:
        incident_data: Dictionary containing incident information
        workflow_type: Type of workflow to validate for
        required_fields: Optional list of additional required fields
    
    Returns:
        ValidationResult with validation status and details
    """
    
    # Base required fields for all workflows
    base_required = ["title", "description", "severity", "affected_services"]
    
    # Workflow-specific required fields
    workflow_specific = {
        "sequential": ["reporter", "detection_time"],
        "parallel": ["data_sources", "analysis_scope"],
        "loop": ["monitoring_criteria", "success_criteria", "max_iterations"]
    }
    
    # Combine required fields
    all_required = base_required.copy()
    if workflow_type in workflow_specific:
        all_required.extend(workflow_specific[workflow_type])
    if required_fields:
        all_required.extend(required_fields)
    
    # Check for missing fields
    missing_fields = []
    validation_errors = []
    suggestions = []
    
    for field in all_required:
        if field not in incident_data or not incident_data[field]:
            missing_fields.append(field)
    
    # Validate field formats
    if "severity" in incident_data:
        if incident_data["severity"] not in [e.value for e in IncidentSeverity]:
            validation_errors.append(f"Invalid severity: {incident_data['severity']}. Must be one of: P0, P1, P2, P3")
    
    if "affected_services" in incident_data:
        if not isinstance(incident_data["affected_services"], list):
            validation_errors.append("affected_services must be a list")
        elif len(incident_data["affected_services"]) == 0:
            validation_errors.append("At least one affected service must be specified")
    
    # Generate suggestions
    if missing_fields:
        suggestions.append("Gather missing incident details before proceeding with workflow")
    
    if "detection_time" in missing_fields:
        suggestions.append("Detection time helps with timeline analysis and SLA tracking")
    
    if workflow_type == "parallel" and "data_sources" in missing_fields:
        suggestions.append("Specify data sources for parallel analysis to optimize resource allocation")
    
    return ValidationResult(
        is_valid=len(missing_fields) == 0 and len(validation_errors) == 0,
        missing_fields=missing_fields,
        validation_errors=validation_errors,
        suggestions=suggestions
    )

def track_workflow_progress(
    workflow_id: str,
    workflow_type: str,
    start_time: Optional[str] = None,
    current_status: Optional[str] = None
) -> WorkflowProgress:
    """
    Track and report on workflow execution progress.
    
    Args:
        workflow_id: Unique identifier for the workflow
        workflow_type: Type of workflow being tracked
        start_time: ISO format start time (optional)
        current_status: Current workflow status (optional)
    
    Returns:
        WorkflowProgress with current status and metrics
    """
    
    # In a real implementation, this would query actual workflow state
    # For now, we'll simulate progress tracking
    
    current_time = datetime.now()
    
    if start_time:
        try:
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            elapsed = current_time - start_dt
            elapsed_str = f"{elapsed.total_seconds():.0f} seconds"
        except:
            elapsed_str = "Unknown"
    else:
        elapsed_str = "Unknown"
    
    # Simulate progress based on workflow type
    if workflow_type == "sequential":
        return WorkflowProgress(
            workflow_id=workflow_id,
            status=current_status or "in_progress",
            progress_percentage=45.0,
            current_step="Root Cause Analysis",
            elapsed_time=elapsed_str,
            estimated_remaining="15-20 minutes",
            issues=[]
        )
    elif workflow_type == "parallel":
        return WorkflowProgress(
            workflow_id=workflow_id,
            status=current_status or "in_progress",
            progress_percentage=65.0,
            current_step="Multi-Source Data Collection",
            elapsed_time=elapsed_str,
            estimated_remaining="8-12 minutes",
            issues=["Log source 'service-c' temporarily unavailable"]
        )
    else:  # loop
        return WorkflowProgress(
            workflow_id=workflow_id,
            status=current_status or "monitoring",
            progress_percentage=25.0,
            current_step="System Health Validation - Iteration 3",
            elapsed_time=elapsed_str,
            estimated_remaining="Ongoing monitoring",
            issues=[]
        )
