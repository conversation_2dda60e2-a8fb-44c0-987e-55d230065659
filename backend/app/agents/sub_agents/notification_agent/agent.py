from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm

AGENT_MODEL = "gemini/gemini-2.0-flash"

notification_agent = LlmAgent(
    name="notification_agent",
    model=LiteLlm(AGENT_MODEL),
    description="Handles automated notifications, alerts, and communication delivery across multiple channels.",
    instruction="You are a notification agent. Send appropriate notifications and alerts through various channels based on incident severity and stakeholder preferences.",
    tools=[],
)
