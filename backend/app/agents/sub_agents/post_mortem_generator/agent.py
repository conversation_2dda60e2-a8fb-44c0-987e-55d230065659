from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm
from . import prompt

AGENT_MODEL = "gemini/gemini-2.0-flash"

post_mortem_generator_agent = LlmAgent(
    name="post_mortem_generator",
    model=LiteLlm(AGENT_MODEL),
    description="Generates comprehensive post-mortem reports with timeline, root cause analysis, and improvement recommendations.",
    instruction=prompt.INSTRUCTION,
    tools=[],
)
