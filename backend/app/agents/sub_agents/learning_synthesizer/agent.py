from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm

AGENT_MODEL = "gemini/gemini-2.0-flash"

learning_synthesizer_agent = LlmAgent(
    name="learning_synthesizer",
    model=LiteLlm(AGENT_MODEL),
    description="Synthesizes insights from incidents to improve processes and prevent future occurrences.",
    instruction="You are a learning synthesizer. Analyze incident patterns, synthesize insights, and recommend process improvements to prevent future occurrences.",
    tools=[],
)
