from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm
from pydantic import BaseModel, Field
from . import prompt


class AIAnalysisOutputSchema(BaseModel):
    root_cause: str = Field(
        ..., description="The identified root cause of the incident."
    )
    immediate_action: str = Field(
        ..., description="Immediate action taken to mitigate the incident."
    )
    impact_forecast: str = Field(
        ..., description="Forecast of the impact of the incident on the system."
    )
    cascading_risks: str = Field(
        ..., description="Any cascading risks associated with the incident."
    )


AGENT_MODEL = "gemini/gemini-2.0-flash"
root_cause_analyzer = LlmAgent(
    name="root_cause_analyzer",
    model=LiteLlm(AGENT_MODEL),
    description="Analyzes technical details of an incident to determine root cause, immediate action, impact forecast, and cascading risks.",
    instruction=prompt.INSTRUCTION,
    output_schema=AIAnalysisOutputSchema,
    output_key="ai_analysis",
    tools=[],
)
