from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm
from pydantic import BaseModel, Field
from typing import List, Dict, Optional
from google.adk.events import Event, EventActions

from . import prompt, tools

class ValidationResult(BaseModel):
    validation_passed: bool = Field(..., description="Whether validation criteria are met")
    success_criteria_status: Dict[str, bool] = Field(..., description="Status of each success criterion")
    failed_criteria: List[str] = Field(..., description="List of criteria that failed validation")
    confidence_score: float = Field(..., description="Confidence in validation results (0.0-1.0)")
    recommendations: List[str] = Field(..., description="Recommendations based on validation")
    should_continue_monitoring: bool = Field(..., description="Whether monitoring should continue")
    escalation_required: bool = Field(..., description="Whether escalation is needed")

AGENT_MODEL = "gemini/gemini-2.0-flash"

validation_agent = LlmAgent(
    name="validation_agent",
    model=LiteLlm(AGENT_MODEL),
    description="Specialized agent for validating success criteria, determining loop continuation, and triggering escalation in monitoring workflows.",
    instruction=prompt.INSTRUCTION,
    output_schema=ValidationResult,
    output_key="validation_results",
    tools=[
        tools.validate_success_criteria,
        tools.check_stability_duration,
        tools.assess_recovery_confidence,
        tools.determine_escalation_need,
    ],
)
