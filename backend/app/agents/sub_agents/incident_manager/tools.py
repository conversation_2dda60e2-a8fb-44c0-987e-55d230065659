import os
from contextlib import contextmanager
from typing import Optional
from uuid import UUID

from database.core import get_db
from db_services import incident as incident_db_service


@contextmanager
def get_db_session():
    db = next(get_db())
    try:
        yield db
    finally:
        db.close()


def get_incident_details(
    incident_id: Optional[str] = None,
    incident_number: Optional[str] = None,
) -> dict:
    """Retrieves the details of a specific incident.

    Args:
        incident_id: The UUID of the incident to retrieve.
        incident_number: The incident number of the incident to retrieve.

    Returns:
        dict: A dictionary containing the incident details.

    Raises:
        ValueError: If neither incident_id nor incident_number is provided,
                   or if the incident is not found.
        Exception: For database or other unexpected errors.
    """
    if not incident_id and not incident_number:
        raise ValueError("Must specify either incident_id or incident_number")

    try:
        print(f"Getting incident details from {os.getenv('DATABASE_URL')}")
        with get_db_session() as db:
            if incident_number:
                incident = incident_db_service.get_incident_by_number(
                    db, incident_number
                )
            elif incident_id:
                try:
                    incident_uuid = UUID(incident_id)
                except ValueError as e:
                    raise ValueError(
                        f"Invalid incident_id format: {incident_id}"
                    ) from e

                incident = incident_db_service.get_incident_by_id(db, incident_uuid)
            if not incident:
                raise ValueError(f"Incident not found with id: {incident_id}")

            result = {
                "incident_id": str(incident.id),
                "incident_number": incident.incident_number,
                "title": incident.title,
                "summary": incident.summary,
                "priority": incident.priority.value if incident.priority else None,
                "severity": incident.severity.value if incident.severity else None,
                "incident_type": incident.incident_type.value
                if incident.incident_type
                else None,
                "status": incident.status.value if incident.status else None,
                "reported_at": incident.reported_at.isoformat()
                if incident.reported_at
                else None,
                "updated_at": incident.updated_at.isoformat()
                if incident.updated_at
                else None,
            }

            return result

    except ValueError:
        raise
    except Exception as e:
        print(f"Unexpected error retrieving incident details: {str(e)}")
        raise Exception(f"Failed to retrieve incident details: {str(e)}") from e
