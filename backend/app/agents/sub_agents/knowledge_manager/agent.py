from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm

AGENT_MODEL = "gemini/gemini-2.0-flash"

knowledge_manager_agent = LlmAgent(
    name="knowledge_manager",
    model=LiteLlm(AGENT_MODEL),
    description="Manages incident knowledge base, retrieves relevant documentation, and captures lessons learned.",
    instruction="You are a knowledge manager. Retrieve relevant documentation, manage the incident knowledge base, and capture lessons learned for future reference.",
    tools=[],
)
