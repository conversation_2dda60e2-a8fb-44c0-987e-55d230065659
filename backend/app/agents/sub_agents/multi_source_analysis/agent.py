from google.adk.agents import ParallelAgent
from google.adk.models.lite_llm import LiteLlm

from . import prompt

AGENT_MODEL = "gemini/gemini-2.0-flash"

# Import the specialized agents that will run in parallel
from ..log_analytics.agent import log_analytics_agent
from ..metrics_analyzer.agent import metrics_analyzer_agent
from ..system_health_analyzer.agent import system_health_analyzer_agent
from ..root_cause_analyzer.agent import root_cause_analyzer

multi_source_analysis_agent = ParallelAgent(
    name="multi_source_analysis",
    description="Parallel workflow agent that simultaneously analyzes multiple data sources for comprehensive incident investigation.",
    sub_agents=[
        log_analytics_agent,        # Parallel: Analyze logs from multiple services
        metrics_analyzer_agent,     # Parallel: Analyze performance metrics
        system_health_analyzer_agent, # Parallel: Check system health status
        root_cause_analyzer,        # Parallel: Perform root cause analysis
    ]
)
