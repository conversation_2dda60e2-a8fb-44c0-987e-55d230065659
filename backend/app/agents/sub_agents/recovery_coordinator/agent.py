from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm

AGENT_MODEL = "gemini/gemini-2.0-flash"

recovery_coordinator_agent = LlmAgent(
    name="recovery_coordinator",
    model=LiteLlm(AGENT_MODEL),
    description="Coordinates manual recovery efforts and validates system restoration.",
    instruction="You are a recovery coordinator. Orchestrate manual recovery efforts, validate system restoration, and ensure proper recovery procedures are followed.",
    tools=[],
)
