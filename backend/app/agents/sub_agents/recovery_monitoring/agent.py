from google.adk.agents import LoopAgent
from google.adk.models.lite_llm import LiteLlm

from . import prompt

AGENT_MODEL = "gemini/gemini-2.0-flash"

# Import the specialized agents that will run in the monitoring loop
from ..system_health_analyzer.agent import system_health_analyzer_agent
from ..recovery_coordinator.agent import recovery_coordinator_agent
from ..validation_agent.agent import validation_agent

recovery_monitoring_agent = LoopAgent(
    name="recovery_monitoring",
    description="Loop workflow agent that continuously monitors system recovery and validates success criteria until incident resolution.",
    max_iterations=20,  # Maximum monitoring cycles to prevent infinite loops
    sub_agents=[
        system_health_analyzer_agent,  # Check current system health status
        recovery_coordinator_agent,    # Coordinate any needed recovery actions
        validation_agent,              # Validate success criteria and determine if loop should continue
    ]
)
