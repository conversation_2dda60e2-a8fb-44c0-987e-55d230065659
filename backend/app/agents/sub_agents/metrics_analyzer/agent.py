from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm
from pydantic import BaseModel, Field
from typing import List, Dict, Optional
from datetime import datetime

from . import prompt, tools

class MetricAnomaly(BaseModel):
    metric_name: str = Field(..., description="Name of the metric")
    current_value: float = Field(..., description="Current metric value")
    baseline_value: float = Field(..., description="Baseline/expected value")
    deviation_percentage: float = Field(..., description="Percentage deviation from baseline")
    severity: str = Field(..., description="Anomaly severity (critical/high/medium/low)")
    trend: str = Field(..., description="Trend direction (increasing/decreasing/stable)")

class PerformanceAnalysis(BaseModel):
    overall_health_score: float = Field(..., description="Overall system health score (0-100)")
    critical_anomalies: List[MetricAnomaly] = Field(..., description="Critical metric anomalies")
    performance_trends: Dict[str, str] = Field(..., description="Performance trends by category")
    resource_utilization: Dict[str, float] = Field(..., description="Resource utilization percentages")
    bottleneck_analysis: List[str] = Field(..., description="Identified performance bottlenecks")
    capacity_warnings: List[str] = Field(..., description="Capacity-related warnings")
    recommendations: List[str] = Field(..., description="Performance improvement recommendations")
    confidence_score: float = Field(..., description="Analysis confidence (0.0-1.0)")

AGENT_MODEL = "gemini/gemini-2.0-flash"

metrics_analyzer_agent = LlmAgent(
    name="metrics_analyzer",
    model=LiteLlm(AGENT_MODEL),
    description="Specialized agent for analyzing system performance metrics, identifying anomalies, and providing performance insights.",
    instruction=prompt.INSTRUCTION,
    output_schema=PerformanceAnalysis,
    output_key="metrics_analysis_results",
    tools=[
        tools.fetch_performance_metrics,
        tools.calculate_baseline_deviations,
        tools.analyze_resource_utilization,
        tools.detect_performance_bottlenecks,
        tools.generate_capacity_forecast,
        tools.correlate_metrics_with_incidents,
    ],
)
