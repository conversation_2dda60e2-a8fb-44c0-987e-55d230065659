from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm

AGENT_MODEL = "gemini/gemini-2.0-flash"

auto_remediation_agent = LlmAgent(
    name="auto_remediation_agent",
    model=LiteLlm(AGENT_MODEL),
    description="Performs automated remediation actions based on runbooks and safety criteria.",
    instruction="You are an automated remediation agent. Execute safe, pre-approved remediation actions based on incident analysis and runbooks.",
    tools=[],
)
