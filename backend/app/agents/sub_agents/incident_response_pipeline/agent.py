from google.adk.agents import SequentialAgent
from google.adk.models.lite_llm import LiteLlm

from . import prompt

AGENT_MODEL = "gemini/gemini-2.0-flash"

# Import the specialized agents that will be part of the pipeline
# These will be created in subsequent steps
from ..incident_triage.agent import incident_triage_agent
from ..incident_manager.agent import incident_manager_agent
from ..root_cause_analyzer.agent import root_cause_analyzer
from ..runbook_generator_agent.agent import runbook_generator_agent
from ..auto_remediation.agent import auto_remediation_agent
from ..recovery_coordinator.agent import recovery_coordinator_agent
from ..communication_specialist.agent import communication_specialist_agent

incident_response_pipeline_agent = SequentialAgent(
    name="incident_response_pipeline",
    description="Sequential workflow agent that orchestrates the complete incident response process from triage to recovery.",
    sub_agents=[
        incident_triage_agent,           # Step 1: Classify and prioritize incident
        incident_manager_agent,          # Step 2: Create/update incident record
        root_cause_analyzer,             # Step 3: Analyze root cause
        runbook_generator_agent,         # Step 4: Generate response runbook
        auto_remediation_agent,          # Step 5: Attempt automated remediation
        recovery_coordinator_agent,      # Step 6: Coordinate recovery efforts
        communication_specialist_agent,  # Step 7: Handle stakeholder communication
    ]
)
