from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm
from . import prompt

AGENT_MODEL = "gemini/gemini-2.0-flash"

incident_escalation_agent = LlmAgent(
    name="incident_escalation_agent",
    model=LiteLlm(AGENT_MODEL),
    description="Handles incident escalation procedures, notifications, and stakeholder management.",
    instruction=prompt.INSTRUCTION,
    tools=[],
)
