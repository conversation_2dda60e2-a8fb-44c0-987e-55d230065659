from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm
from pydantic import BaseModel, Field
from typing import List, Optional
from enum import Enum

from . import prompt, tools

class IncidentSeverity(str, Enum):
    P0 = "P0"  # Critical - Complete service outage
    P1 = "P1"  # High - Major functionality impacted
    P2 = "P2"  # Medium - Minor functionality impacted
    P3 = "P3"  # Low - Minimal impact

class IncidentCategory(str, Enum):
    SERVICE_OUTAGE = "service_outage"
    PERFORMANCE_DEGRADATION = "performance_degradation"
    SECURITY_INCIDENT = "security_incident"
    DATA_CORRUPTION = "data_corruption"
    INFRASTRUCTURE_FAILURE = "infrastructure_failure"
    DEPLOYMENT_ISSUE = "deployment_issue"

class TriageResult(BaseModel):
    severity: IncidentSeverity = Field(..., description="Incident severity classification")
    category: IncidentCategory = Field(..., description="Incident category classification")
    priority_score: int = Field(..., description="Priority score (1-10, 10 being highest)")
    affected_services: List[str] = Field(..., description="List of affected services")
    estimated_impact: str = Field(..., description="Estimated business and technical impact")
    escalation_required: bool = Field(..., description="Whether immediate escalation is required")
    escalation_targets: List[str] = Field(default=[], description="Who should be escalated to")
    initial_response_actions: List[str] = Field(..., description="Immediate actions to take")
    sla_deadline: str = Field(..., description="SLA deadline for resolution")
    confidence_score: float = Field(..., description="Confidence in triage assessment (0.0-1.0)")

AGENT_MODEL = "gemini/gemini-2.0-flash"

incident_triage_agent = LlmAgent(
    name="incident_triage_agent",
    model=LiteLlm(AGENT_MODEL),
    description="Specialized agent for rapid incident classification, severity assessment, and initial response coordination.",
    instruction=prompt.INSTRUCTION,
    output_schema=TriageResult,
    output_key="incident_classification",
    tools=[
        tools.analyze_service_dependencies,
        tools.check_historical_incidents,
        tools.calculate_business_impact,
        tools.determine_escalation_path,
        tools.get_current_system_status,
    ],
)
