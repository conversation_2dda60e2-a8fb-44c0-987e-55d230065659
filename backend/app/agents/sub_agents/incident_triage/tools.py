"""Tools for the incident triage agent."""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
import json

class ServiceDependency(BaseModel):
    service_name: str = Field(..., description="Name of the service")
    dependency_type: str = Field(..., description="Type of dependency (upstream/downstream/peer)")
    criticality: str = Field(..., description="Criticality level (critical/important/optional)")
    impact_if_failed: str = Field(..., description="Impact if this dependency fails")

class DependencyAnalysis(BaseModel):
    primary_service: str = Field(..., description="Primary affected service")
    upstream_dependencies: List[ServiceDependency] = Field(..., description="Services this depends on")
    downstream_dependencies: List[ServiceDependency] = Field(..., description="Services that depend on this")
    cascade_risk: str = Field(..., description="Risk of cascade failure (high/medium/low)")
    isolation_possible: bool = Field(..., description="Whether service can be isolated")

class HistoricalIncident(BaseModel):
    incident_id: str = Field(..., description="Historical incident identifier")
    title: str = Field(..., description="Incident title")
    severity: str = Field(..., description="Incident severity")
    resolution_time: str = Field(..., description="Time to resolution")
    root_cause: str = Field(..., description="Root cause summary")
    resolution_summary: str = Field(..., description="How it was resolved")
    similarity_score: float = Field(..., description="Similarity to current incident (0.0-1.0)")

class BusinessImpact(BaseModel):
    estimated_affected_users: int = Field(..., description="Number of affected users")
    revenue_impact_per_hour: float = Field(..., description="Estimated revenue impact per hour")
    customer_tier_impact: str = Field(..., description="Impact on customer tiers (enterprise/standard/free)")
    sla_breach_risk: str = Field(..., description="Risk of SLA breach (high/medium/low)")
    regulatory_implications: List[str] = Field(default=[], description="Any regulatory concerns")
    business_criticality: str = Field(..., description="Business criticality (critical/high/medium/low)")

class EscalationPath(BaseModel):
    immediate_escalation_required: bool = Field(..., description="Whether immediate escalation is needed")
    primary_escalation_target: str = Field(..., description="Primary person/team to escalate to")
    secondary_escalation_targets: List[str] = Field(default=[], description="Secondary escalation options")
    escalation_timeline: str = Field(..., description="When to escalate if not resolved")
    required_expertise: List[str] = Field(..., description="Types of expertise needed")
    on_call_contacts: List[str] = Field(default=[], description="Current on-call contacts")

class SystemStatus(BaseModel):
    overall_health: str = Field(..., description="Overall system health (healthy/degraded/critical)")
    affected_services: List[str] = Field(..., description="Currently affected services")
    service_statuses: Dict[str, str] = Field(..., description="Individual service statuses")
    active_alerts: List[str] = Field(default=[], description="Currently active alerts")
    recent_deployments: List[str] = Field(default=[], description="Recent deployments that might be related")
    infrastructure_status: str = Field(..., description="Infrastructure health status")

def analyze_service_dependencies(
    primary_service: str,
    incident_description: str,
    affected_services: Optional[List[str]] = None
) -> DependencyAnalysis:
    """
    Analyze service dependencies to understand potential cascade effects.
    
    Args:
        primary_service: The primary affected service
        incident_description: Description of the incident
        affected_services: List of known affected services
    
    Returns:
        DependencyAnalysis with dependency mapping and cascade risk assessment
    """
    
    # In a real implementation, this would query service dependency databases
    # For now, we'll simulate dependency analysis based on common patterns
    
    # Simulate dependency mapping based on service name patterns
    upstream_deps = []
    downstream_deps = []
    
    # Common dependency patterns
    if "api" in primary_service.lower():
        upstream_deps.extend([
            ServiceDependency(
                service_name="database-primary",
                dependency_type="upstream",
                criticality="critical",
                impact_if_failed="Complete service failure"
            ),
            ServiceDependency(
                service_name="cache-redis",
                dependency_type="upstream", 
                criticality="important",
                impact_if_failed="Performance degradation"
            )
        ])
        downstream_deps.extend([
            ServiceDependency(
                service_name="web-frontend",
                dependency_type="downstream",
                criticality="critical",
                impact_if_failed="User interface failures"
            ),
            ServiceDependency(
                service_name="mobile-app",
                dependency_type="downstream",
                criticality="important",
                impact_if_failed="Mobile functionality loss"
            )
        ])
    
    elif "database" in primary_service.lower():
        downstream_deps.extend([
            ServiceDependency(
                service_name="api-service",
                dependency_type="downstream",
                criticality="critical",
                impact_if_failed="API service failure"
            ),
            ServiceDependency(
                service_name="analytics-service",
                dependency_type="downstream",
                criticality="optional",
                impact_if_failed="Analytics data loss"
            )
        ])
    
    # Determine cascade risk
    critical_deps = len([d for d in upstream_deps + downstream_deps if d.criticality == "critical"])
    if critical_deps > 2:
        cascade_risk = "high"
    elif critical_deps > 0:
        cascade_risk = "medium"
    else:
        cascade_risk = "low"
    
    return DependencyAnalysis(
        primary_service=primary_service,
        upstream_dependencies=upstream_deps,
        downstream_dependencies=downstream_deps,
        cascade_risk=cascade_risk,
        isolation_possible=cascade_risk != "high"
    )

def check_historical_incidents(
    incident_description: str,
    affected_services: List[str],
    incident_type: Optional[str] = None,
    limit: int = 5
) -> List[HistoricalIncident]:
    """
    Find similar historical incidents to learn from past responses.
    
    Args:
        incident_description: Current incident description
        affected_services: List of affected services
        incident_type: Type of incident (optional)
        limit: Maximum number of historical incidents to return
    
    Returns:
        List of similar historical incidents with resolution information
    """
    
    # In a real implementation, this would query incident databases with similarity search
    # For now, we'll simulate historical incident matching
    
    historical_incidents = []
    
    # Simulate finding similar incidents based on keywords
    if "database" in incident_description.lower() or any("db" in svc.lower() for svc in affected_services):
        historical_incidents.append(HistoricalIncident(
            incident_id="INC-2024-0156",
            title="Database connection pool exhaustion",
            severity="P1",
            resolution_time="45 minutes",
            root_cause="Connection pool misconfiguration after deployment",
            resolution_summary="Increased connection pool size and restarted application servers",
            similarity_score=0.85
        ))
    
    if "performance" in incident_description.lower() or "slow" in incident_description.lower():
        historical_incidents.append(HistoricalIncident(
            incident_id="INC-2024-0089",
            title="API response time degradation",
            severity="P2",
            resolution_time="30 minutes",
            root_cause="Database query optimization needed",
            resolution_summary="Applied database index optimization and query caching",
            similarity_score=0.72
        ))
    
    if "outage" in incident_description.lower() or "down" in incident_description.lower():
        historical_incidents.append(HistoricalIncident(
            incident_id="INC-2024-0201",
            title="Complete service outage",
            severity="P0",
            resolution_time="25 minutes",
            root_cause="Load balancer configuration error",
            resolution_summary="Reverted load balancer configuration and restarted services",
            similarity_score=0.78
        ))
    
    # Sort by similarity score and return top results
    historical_incidents.sort(key=lambda x: x.similarity_score, reverse=True)
    return historical_incidents[:limit]

def calculate_business_impact(
    affected_services: List[str],
    incident_severity: str,
    incident_description: str,
    time_of_day: Optional[str] = None
) -> BusinessImpact:
    """
    Calculate estimated business impact for prioritization.
    
    Args:
        affected_services: List of affected services
        incident_severity: Incident severity level
        incident_description: Description of the incident
        time_of_day: Time when incident occurred (optional)
    
    Returns:
        BusinessImpact with quantified impact estimates
    """
    
    # Base impact calculations (would be based on real metrics in production)
    base_users = 10000  # Base user count
    base_revenue_per_hour = 5000.0  # Base revenue per hour
    
    # Service impact multipliers
    service_multipliers = {
        "api": 1.5,
        "database": 2.0,
        "frontend": 1.2,
        "payment": 3.0,
        "auth": 2.5
    }
    
    # Calculate service impact multiplier
    total_multiplier = 1.0
    for service in affected_services:
        for key, multiplier in service_multipliers.items():
            if key in service.lower():
                total_multiplier *= multiplier
                break
    
    # Severity impact multipliers
    severity_multipliers = {
        "P0": 3.0,
        "P1": 2.0,
        "P2": 1.2,
        "P3": 1.0
    }
    
    severity_mult = severity_multipliers.get(incident_severity, 1.0)
    
    # Time of day impact (business hours vs off-hours)
    time_multiplier = 1.5 if time_of_day and "business" in time_of_day.lower() else 1.0
    
    # Calculate final impact
    affected_users = int(base_users * total_multiplier * severity_mult * time_multiplier)
    revenue_impact = base_revenue_per_hour * total_multiplier * severity_mult * time_multiplier
    
    # Determine customer tier impact
    if incident_severity in ["P0", "P1"]:
        customer_tier_impact = "All tiers affected"
    elif incident_severity == "P2":
        customer_tier_impact = "Standard and Enterprise tiers affected"
    else:
        customer_tier_impact = "Limited tier impact"
    
    # SLA breach risk
    sla_risk = "high" if incident_severity in ["P0", "P1"] else "medium" if incident_severity == "P2" else "low"
    
    # Business criticality
    if any(critical_svc in svc.lower() for svc in affected_services for critical_svc in ["payment", "auth", "api"]):
        business_criticality = "critical"
    elif incident_severity in ["P0", "P1"]:
        business_criticality = "high"
    elif incident_severity == "P2":
        business_criticality = "medium"
    else:
        business_criticality = "low"
    
    return BusinessImpact(
        estimated_affected_users=affected_users,
        revenue_impact_per_hour=revenue_impact,
        customer_tier_impact=customer_tier_impact,
        sla_breach_risk=sla_risk,
        regulatory_implications=["Data protection compliance"] if "data" in incident_description.lower() else [],
        business_criticality=business_criticality
    )

def determine_escalation_path(
    incident_severity: str,
    affected_services: List[str],
    incident_type: str,
    current_time: Optional[str] = None
) -> EscalationPath:
    """
    Determine appropriate escalation path based on incident characteristics.
    
    Args:
        incident_severity: Severity level of the incident
        affected_services: List of affected services
        incident_type: Type of incident
        current_time: Current time for on-call determination
    
    Returns:
        EscalationPath with escalation recommendations
    """
    
    # Immediate escalation rules
    immediate_escalation = incident_severity in ["P0", "P1"] or incident_type == "security_incident"
    
    # Determine primary escalation target based on services and type
    if incident_type == "security_incident":
        primary_target = "Security Team Lead"
        required_expertise = ["Security Analysis", "Incident Response", "Forensics"]
    elif any("database" in svc.lower() for svc in affected_services):
        primary_target = "Database Team Lead"
        required_expertise = ["Database Administration", "Performance Tuning"]
    elif any("api" in svc.lower() for svc in affected_services):
        primary_target = "Backend Team Lead"
        required_expertise = ["API Development", "System Architecture"]
    else:
        primary_target = "On-Call Engineer"
        required_expertise = ["General SRE", "System Administration"]
    
    # Secondary escalation targets
    secondary_targets = []
    if incident_severity == "P0":
        secondary_targets.extend(["Engineering Manager", "CTO"])
    if incident_type == "security_incident":
        secondary_targets.extend(["CISO", "Legal Team"])
    
    # Escalation timeline
    if incident_severity == "P0":
        escalation_timeline = "Escalate immediately if no response in 5 minutes"
    elif incident_severity == "P1":
        escalation_timeline = "Escalate if no progress in 15 minutes"
    elif incident_severity == "P2":
        escalation_timeline = "Escalate if no resolution in 1 hour"
    else:
        escalation_timeline = "Escalate if no resolution in 4 hours"
    
    # Mock on-call contacts (would be from real on-call system)
    on_call_contacts = [
        "<EMAIL> (Primary SRE)",
        "<EMAIL> (Secondary SRE)",
        "<EMAIL> (Team Lead)"
    ]
    
    return EscalationPath(
        immediate_escalation_required=immediate_escalation,
        primary_escalation_target=primary_target,
        secondary_escalation_targets=secondary_targets,
        escalation_timeline=escalation_timeline,
        required_expertise=required_expertise,
        on_call_contacts=on_call_contacts
    )

def get_current_system_status(
    services_to_check: Optional[List[str]] = None
) -> SystemStatus:
    """
    Get current system health status for validation and context.
    
    Args:
        services_to_check: Specific services to check (optional)
    
    Returns:
        SystemStatus with current system health information
    """
    
    # In a real implementation, this would query monitoring systems
    # For now, we'll simulate system status
    
    # Simulate service statuses
    service_statuses = {}
    affected_services = []
    
    if services_to_check:
        for service in services_to_check:
            # Simulate some services being affected
            if "api" in service.lower():
                service_statuses[service] = "degraded"
                affected_services.append(service)
            elif "database" in service.lower():
                service_statuses[service] = "critical"
                affected_services.append(service)
            else:
                service_statuses[service] = "healthy"
    else:
        # Default service status check
        service_statuses = {
            "api-service": "degraded",
            "web-frontend": "healthy",
            "database-primary": "critical",
            "cache-redis": "healthy",
            "payment-service": "healthy"
        }
        affected_services = ["api-service", "database-primary"]
    
    # Determine overall health
    if any(status == "critical" for status in service_statuses.values()):
        overall_health = "critical"
    elif any(status == "degraded" for status in service_statuses.values()):
        overall_health = "degraded"
    else:
        overall_health = "healthy"
    
    # Simulate active alerts
    active_alerts = [
        "High database connection count",
        "API response time above threshold",
        "Disk space warning on db-server-01"
    ]
    
    # Simulate recent deployments
    recent_deployments = [
        "api-service v2.1.3 deployed 2 hours ago",
        "database schema update 4 hours ago"
    ]
    
    return SystemStatus(
        overall_health=overall_health,
        affected_services=affected_services,
        service_statuses=service_statuses,
        active_alerts=active_alerts,
        recent_deployments=recent_deployments,
        infrastructure_status="healthy"
    )
