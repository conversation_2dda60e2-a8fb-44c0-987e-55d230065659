"""Advanced communication patterns for multi-agent coordination."""

from typing import Dict, Any, List, Optional
from datetime import datetime
import json
from enum import Enum

class CommunicationChannel(str, Enum):
    """Communication channels for agent coordination."""
    STATE_SHARED = "state_shared"
    AGENT_TOOL = "agent_tool"
    TRANSFER = "transfer"
    EVENT_STREAM = "event_stream"

class MessagePriority(str, Enum):
    """Message priority levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class AgentCommunicationManager:
    """Manages communication patterns between agents."""
    
    def __init__(self):
        self.message_history = []
        self.active_workflows = {}
        self.agent_status = {}
    
    def create_workflow_context(self, workflow_id: str, workflow_type: str, 
                               incident_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create shared context for workflow execution."""
        context = {
            "workflow_id": workflow_id,
            "workflow_type": workflow_type,
            "created_at": datetime.now().isoformat(),
            "incident_data": incident_data,
            "status": "initialized",
            "participating_agents": [],
            "shared_state": {},
            "communication_log": [],
            "escalation_triggers": [],
            "success_criteria": {}
        }
        
        self.active_workflows[workflow_id] = context
        return context
    
    def update_shared_state(self, workflow_id: str, agent_name: str, 
                           state_key: str, state_value: Any) -> None:
        """Update shared state with agent contributions."""
        if workflow_id in self.active_workflows:
            workflow = self.active_workflows[workflow_id]
            
            # Update shared state
            workflow["shared_state"][state_key] = {
                "value": state_value,
                "updated_by": agent_name,
                "updated_at": datetime.now().isoformat()
            }
            
            # Log the communication
            self.log_communication(
                workflow_id, agent_name, "state_update", 
                f"Updated {state_key}", MessagePriority.MEDIUM
            )
    
    def log_communication(self, workflow_id: str, agent_name: str, 
                         message_type: str, message: str, 
                         priority: MessagePriority) -> None:
        """Log communication between agents."""
        communication_entry = {
            "timestamp": datetime.now().isoformat(),
            "workflow_id": workflow_id,
            "agent_name": agent_name,
            "message_type": message_type,
            "message": message,
            "priority": priority.value
        }
        
        self.message_history.append(communication_entry)
        
        if workflow_id in self.active_workflows:
            self.active_workflows[workflow_id]["communication_log"].append(communication_entry)
    
    def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get current workflow status and progress."""
        if workflow_id not in self.active_workflows:
            return {"error": "Workflow not found"}
        
        workflow = self.active_workflows[workflow_id]
        
        # Calculate progress based on shared state
        total_expected_outputs = len(workflow.get("participating_agents", []))
        completed_outputs = len([k for k in workflow["shared_state"].keys() 
                                if k.endswith("_results")])
        
        progress_percentage = (completed_outputs / total_expected_outputs * 100) if total_expected_outputs > 0 else 0
        
        return {
            "workflow_id": workflow_id,
            "status": workflow["status"],
            "progress_percentage": progress_percentage,
            "participating_agents": workflow["participating_agents"],
            "shared_state_keys": list(workflow["shared_state"].keys()),
            "communication_count": len(workflow["communication_log"]),
            "last_activity": workflow["communication_log"][-1]["timestamp"] if workflow["communication_log"] else None
        }
    
    def check_escalation_triggers(self, workflow_id: str) -> List[Dict[str, Any]]:
        """Check if any escalation triggers have been activated."""
        if workflow_id not in self.active_workflows:
            return []
        
        workflow = self.active_workflows[workflow_id]
        triggered_escalations = []
        
        # Check for timeout escalations
        created_time = datetime.fromisoformat(workflow["created_at"])
        elapsed_time = (datetime.now() - created_time).total_seconds()
        
        if elapsed_time > 1800:  # 30 minutes
            triggered_escalations.append({
                "type": "timeout",
                "message": "Workflow has been running for over 30 minutes",
                "severity": "high"
            })
        
        # Check for failed agents
        failed_agents = [agent for agent in workflow["participating_agents"] 
                        if self.agent_status.get(agent, {}).get("status") == "failed"]
        
        if failed_agents:
            triggered_escalations.append({
                "type": "agent_failure",
                "message": f"Agents failed: {', '.join(failed_agents)}",
                "severity": "critical"
            })
        
        return triggered_escalations
    
    def coordinate_parallel_execution(self, workflow_id: str, 
                                    agent_assignments: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Coordinate parallel agent execution with proper state management."""
        if workflow_id not in self.active_workflows:
            return {"error": "Workflow not found"}
        
        workflow = self.active_workflows[workflow_id]
        workflow["participating_agents"] = list(agent_assignments.keys())
        workflow["status"] = "executing_parallel"
        
        # Set up individual agent contexts
        for agent_name, assignment in agent_assignments.items():
            agent_context = {
                "agent_name": agent_name,
                "assignment": assignment,
                "start_time": datetime.now().isoformat(),
                "expected_output_key": f"{agent_name}_results",
                "status": "assigned"
            }
            
            workflow["shared_state"][f"{agent_name}_context"] = agent_context
            
            self.log_communication(
                workflow_id, "coordinator", "assignment", 
                f"Assigned {assignment.get('task', 'unknown task')} to {agent_name}",
                MessagePriority.HIGH
            )
        
        return {
            "workflow_id": workflow_id,
            "parallel_assignments": agent_assignments,
            "coordination_status": "initiated"
        }
    
    def coordinate_sequential_execution(self, workflow_id: str, 
                                      agent_sequence: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Coordinate sequential agent execution with state passing."""
        if workflow_id not in self.active_workflows:
            return {"error": "Workflow not found"}
        
        workflow = self.active_workflows[workflow_id]
        workflow["participating_agents"] = [agent["name"] for agent in agent_sequence]
        workflow["status"] = "executing_sequential"
        
        # Set up sequential execution plan
        workflow["shared_state"]["execution_sequence"] = {
            "agents": agent_sequence,
            "current_step": 0,
            "step_results": []
        }
        
        self.log_communication(
            workflow_id, "coordinator", "sequence_start",
            f"Starting sequential execution with {len(agent_sequence)} agents",
            MessagePriority.HIGH
        )
        
        return {
            "workflow_id": workflow_id,
            "sequence_length": len(agent_sequence),
            "coordination_status": "initiated"
        }
    
    def validate_workflow_completion(self, workflow_id: str) -> Dict[str, Any]:
        """Validate that workflow has completed successfully."""
        if workflow_id not in self.active_workflows:
            return {"error": "Workflow not found"}
        
        workflow = self.active_workflows[workflow_id]
        
        # Check if all expected outputs are present
        expected_outputs = [f"{agent}_results" for agent in workflow["participating_agents"]]
        actual_outputs = [key for key in workflow["shared_state"].keys() if key.endswith("_results")]
        
        missing_outputs = [output for output in expected_outputs if output not in actual_outputs]
        
        completion_status = {
            "workflow_id": workflow_id,
            "is_complete": len(missing_outputs) == 0,
            "missing_outputs": missing_outputs,
            "completion_percentage": (len(actual_outputs) / len(expected_outputs) * 100) if expected_outputs else 100,
            "total_communications": len(workflow["communication_log"]),
            "workflow_duration": self._calculate_duration(workflow["created_at"])
        }
        
        if completion_status["is_complete"]:
            workflow["status"] = "completed"
            workflow["completed_at"] = datetime.now().isoformat()
            
            self.log_communication(
                workflow_id, "coordinator", "completion",
                "Workflow completed successfully",
                MessagePriority.HIGH
            )
        
        return completion_status
    
    def _calculate_duration(self, start_time: str) -> str:
        """Calculate workflow duration."""
        start = datetime.fromisoformat(start_time)
        duration = datetime.now() - start
        return f"{duration.total_seconds():.1f} seconds"

# Global communication manager instance
communication_manager = AgentCommunicationManager()

def get_communication_manager() -> AgentCommunicationManager:
    """Get the global communication manager instance."""
    return communication_manager
