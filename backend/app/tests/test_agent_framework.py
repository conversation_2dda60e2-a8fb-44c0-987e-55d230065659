"""Comprehensive testing framework for SRE agent validation."""

import pytest
import asyncio
from typing import Dict, Any, List
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from agents.main_agent import coordinator_agent
from agents.sub_agents.workflow_orchestrator.agent import workflow_orchestrator_agent
from agents.sub_agents.incident_triage.agent import incident_triage_agent
from agents.sub_agents.system_health_analyzer.agent import system_health_analyzer_agent
from agents.error_handling import error_handler, SREError, <PERSON>rrorCategory, ErrorSeverity
from agents.communication_patterns import communication_manager

class TestIncidentScenarios:
    """Test various incident scenarios and agent responses."""
    
    @pytest.fixture
    def sample_p0_incident(self):
        """Sample P0 incident data for testing."""
        return {
            "title": "Complete API service outage",
            "description": "All API endpoints returning 500 errors, affecting all users",
            "severity": "P0",
            "affected_services": ["api-service", "database", "cache"],
            "detection_time": datetime.now().isoformat(),
            "reporter": "monitoring-system",
            "initial_symptoms": [
                "API response time > 30 seconds",
                "Error rate > 50%",
                "Database connection failures"
            ]
        }
    
    @pytest.fixture
    def sample_p2_incident(self):
        """Sample P2 incident data for testing."""
        return {
            "title": "Slow response times on user dashboard",
            "description": "Dashboard loading 2x slower than normal, affecting 20% of users",
            "severity": "P2",
            "affected_services": ["dashboard-service", "analytics-api"],
            "detection_time": (datetime.now() - timedelta(minutes=15)).isoformat(),
            "reporter": "user-report"
        }
    
    def test_p0_incident_triage(self, sample_p0_incident):
        """Test incident triage for P0 incidents."""
        # Mock the triage agent tools
        with patch.object(incident_triage_agent, 'tools') as mock_tools:
            mock_tools.return_value = {
                "severity": "P0",
                "escalation_required": True,
                "priority_score": 10,
                "confidence_score": 0.95
            }
            
            # Test triage response
            result = self._simulate_agent_call(incident_triage_agent, sample_p0_incident)
            
            assert result["severity"] == "P0"
            assert result["escalation_required"] is True
            assert result["priority_score"] >= 8
            assert result["confidence_score"] >= 0.8
    
    def test_workflow_orchestrator_routing(self, sample_p0_incident):
        """Test workflow orchestrator routing decisions."""
        # Test that P0 incidents are routed to sequential workflow
        with patch.object(workflow_orchestrator_agent, 'tools') as mock_tools:
            mock_tools.return_value = {
                "workflow_type": "sequential",
                "confidence": 0.90,
                "reasoning": "Critical incident requires structured sequential response"
            }
            
            result = self._simulate_agent_call(workflow_orchestrator_agent, sample_p0_incident)
            
            assert result["workflow_type"] == "sequential"
            assert result["confidence"] >= 0.8
    
    def test_parallel_workflow_coordination(self):
        """Test parallel workflow coordination."""
        workflow_id = "test-parallel-001"
        
        # Create workflow context
        context = communication_manager.create_workflow_context(
            workflow_id, "parallel", {"test": "data"}
        )
        
        # Set up parallel execution
        agent_assignments = {
            "log_analytics": {"task": "analyze_logs", "time_range": "1h"},
            "metrics_analyzer": {"task": "analyze_metrics", "services": ["api"]},
            "system_health": {"task": "check_health", "components": ["all"]}
        }
        
        coordination_result = communication_manager.coordinate_parallel_execution(
            workflow_id, agent_assignments
        )
        
        assert coordination_result["coordination_status"] == "initiated"
        assert len(coordination_result["parallel_assignments"]) == 3
        
        # Simulate agent completions
        communication_manager.update_shared_state(
            workflow_id, "log_analytics", "log_analytics_results", 
            {"errors_found": 5, "patterns": ["connection_timeout"]}
        )
        
        communication_manager.update_shared_state(
            workflow_id, "metrics_analyzer", "metrics_analyzer_results",
            {"cpu_spike": True, "memory_usage": 85}
        )
        
        # Check workflow status
        status = communication_manager.get_workflow_status(workflow_id)
        assert status["progress_percentage"] > 0
        assert len(status["shared_state_keys"]) >= 2
    
    def test_error_handling_and_recovery(self):
        """Test error handling and recovery mechanisms."""
        # Test SRE error creation and handling
        test_error = SREError(
            message="Database connection timeout",
            category=ErrorCategory.COMMUNICATION,
            severity=ErrorSeverity.HIGH,
            context={"database": "primary", "timeout": 30}
        )
        
        result = error_handler.handle_error(
            test_error, "test_agent", "database_query"
        )
        
        assert result["error_handled"] is True
        assert result["severity"] == "high"
        assert "Database connection timeout" in result["user_message"]
    
    def test_guardrail_system(self):
        """Test enhanced guardrail system."""
        from agents.guardrail import SREGuardrailSystem
        
        guardrail = SREGuardrailSystem()
        
        # Test dangerous operation detection
        dangerous_text = "Please run rm -rf /production/data to clean up"
        risk_assessment = guardrail.check_high_risk_operations(dangerous_text)
        
        assert risk_assessment["risk_level"] == "critical"
        assert risk_assessment["requires_confirmation"] is True
        assert len(risk_assessment["detected_risks"]) > 0
        
        # Test incident validation
        incomplete_incident = {"title": "Test incident"}
        validation = guardrail.validate_incident_data(str(incomplete_incident))
        
        assert len(validation["warnings"]) > 0
        assert "description" in str(validation["warnings"])
    
    def test_sequential_workflow_execution(self):
        """Test sequential workflow execution and state passing."""
        workflow_id = "test-sequential-001"
        
        # Create workflow context
        context = communication_manager.create_workflow_context(
            workflow_id, "sequential", {"incident_id": "INC-001"}
        )
        
        # Define agent sequence
        agent_sequence = [
            {"name": "incident_triage", "task": "classify_incident"},
            {"name": "root_cause_analyzer", "task": "analyze_cause"},
            {"name": "runbook_generator", "task": "create_runbook"}
        ]
        
        coordination_result = communication_manager.coordinate_sequential_execution(
            workflow_id, agent_sequence
        )
        
        assert coordination_result["coordination_status"] == "initiated"
        assert coordination_result["sequence_length"] == 3
        
        # Simulate sequential execution
        step_results = [
            {"severity": "P1", "category": "performance"},
            {"root_cause": "database_overload", "confidence": 0.85},
            {"runbook_steps": 5, "estimated_time": "30min"}
        ]
        
        for i, result in enumerate(step_results):
            agent_name = agent_sequence[i]["name"]
            communication_manager.update_shared_state(
                workflow_id, agent_name, f"{agent_name}_results", result
            )
        
        # Validate completion
        completion_status = communication_manager.validate_workflow_completion(workflow_id)
        assert completion_status["is_complete"] is True
        assert completion_status["completion_percentage"] == 100.0
    
    def test_escalation_triggers(self):
        """Test escalation trigger detection."""
        workflow_id = "test-escalation-001"
        
        # Create workflow with old timestamp to trigger timeout
        context = communication_manager.create_workflow_context(
            workflow_id, "sequential", {"test": "data"}
        )
        
        # Manually set old timestamp
        context["created_at"] = (datetime.now() - timedelta(minutes=35)).isoformat()
        
        escalations = communication_manager.check_escalation_triggers(workflow_id)
        
        # Should trigger timeout escalation
        timeout_escalations = [e for e in escalations if e["type"] == "timeout"]
        assert len(timeout_escalations) > 0
        assert timeout_escalations[0]["severity"] == "high"
    
    def test_agent_communication_patterns(self):
        """Test agent communication and state sharing."""
        workflow_id = "test-communication-001"
        
        # Create workflow
        context = communication_manager.create_workflow_context(
            workflow_id, "parallel", {"incident": "test"}
        )
        
        # Test state updates from multiple agents
        agents_data = {
            "agent_a": {"status": "completed", "findings": ["error1", "error2"]},
            "agent_b": {"status": "in_progress", "progress": 75},
            "agent_c": {"status": "completed", "recommendations": ["fix1", "fix2"]}
        }
        
        for agent_name, data in agents_data.items():
            communication_manager.update_shared_state(
                workflow_id, agent_name, f"{agent_name}_status", data
            )
        
        # Verify communication logging
        workflow = communication_manager.active_workflows[workflow_id]
        assert len(workflow["communication_log"]) >= 3
        
        # Verify shared state
        assert len(workflow["shared_state"]) >= 3
        for agent_name in agents_data.keys():
            assert f"{agent_name}_status" in workflow["shared_state"]
    
    def _simulate_agent_call(self, agent, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate an agent call for testing purposes."""
        # This would normally involve calling the actual agent
        # For testing, we return mock responses based on the agent type
        
        if agent.name == "incident_triage_agent":
            return {
                "severity": input_data.get("severity", "P2"),
                "escalation_required": input_data.get("severity") in ["P0", "P1"],
                "priority_score": 10 if input_data.get("severity") == "P0" else 5,
                "confidence_score": 0.9
            }
        elif agent.name == "workflow_orchestrator":
            severity = input_data.get("severity", "P2")
            if severity in ["P0", "P1"]:
                return {
                    "workflow_type": "sequential",
                    "confidence": 0.9,
                    "reasoning": "High severity incident requires structured response"
                }
            else:
                return {
                    "workflow_type": "parallel",
                    "confidence": 0.8,
                    "reasoning": "Standard incident can benefit from parallel analysis"
                }
        
        return {"status": "success", "mock": True}

class TestAgentIntegration:
    """Integration tests for agent interactions."""
    
    def test_end_to_end_incident_response(self):
        """Test complete incident response workflow."""
        # This would test the full flow from incident report to resolution
        incident_data = {
            "title": "API service degradation",
            "description": "Response times increased by 300%",
            "severity": "P1",
            "affected_services": ["api-service"]
        }
        
        # Test would simulate:
        # 1. Coordinator receives incident
        # 2. Routes to workflow orchestrator
        # 3. Orchestrator selects appropriate workflow
        # 4. Workflow executes with proper agent coordination
        # 5. Results are aggregated and returned
        
        # For now, we'll test the components individually
        assert True  # Placeholder for full integration test
    
    def test_agent_performance_metrics(self):
        """Test agent performance and response times."""
        # Test would measure:
        # - Agent response times
        # - Resource utilization
        # - Error rates
        # - Workflow completion times
        
        assert True  # Placeholder for performance testing

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
