import { Loader } from '@mantine/core';
import React, { Suspense } from 'react';
import { BrowserRouter, Navigate, Route, Routes } from 'react-router';
import './App.css';
import { Layout } from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';
import { AuthProvider } from './contexts/AuthContext';
import Login from './pages/Login';

import IncidentTimelineView from './components/IncidentDetail/IncidentTimelineView';

const Dashboard = React.lazy(() => import('./pages/Dashboard'));
const Incidents = React.lazy(() => import('./pages/Incidents'));
const IncidentDetail = React.lazy(() => import('./pages/IncidentDetail'));
const CreateIncident = React.lazy(() => import('./pages/CreateIncident'));
const Users = React.lazy(() => import('./pages/Users'));
const Logs = React.lazy(() => import('./pages/Logs'));
const Traces = React.lazy(() => import('./pages/Traces'));
const Metrics = React.lazy(() => import('./pages/Metrics'));
const KnowledgeBase = React.lazy(() => import('./pages/KnowledgeBase'));

const protectedRoutes = [
  { path: '/dashboard', element: <Dashboard /> },
  { path: '/incidents', element: <Incidents /> },
  { path: '/incident/:incidentId', element: <IncidentDetail /> },
  { path: '/incident/:incidentId/timeline', element: <IncidentTimelineView /> },
  { path: '/incident/create', element: <CreateIncident /> },
  { path: '/users', element: <Users /> },
  { path: '/logs', element: <Logs /> },
  { path: '/traces', element: <Traces /> },
  { path: '/metrics', element: <Metrics /> },
  { path: '/knowledge-base', element: <KnowledgeBase /> },
];

function App() {
  return (
    <BrowserRouter>
      <AuthProvider>
        <div className="w-screen h-screen font-jost flex overflow-hidden bg-gray-50">
          <Suspense fallback={<Loader color="blue" />}>
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              {protectedRoutes.map(({ path, element }) => (
                <Route
                  key={path}
                  path={path}
                  element={
                    <ProtectedRoute>
                      <Layout>{element}</Layout>
                    </ProtectedRoute>
                  }
                />
              ))}
              <Route path="*" element={<div>Not Found</div>} />
            </Routes>
          </Suspense>
        </div>
      </AuthProvider>
    </BrowserRouter>
  );
}

export default App;
